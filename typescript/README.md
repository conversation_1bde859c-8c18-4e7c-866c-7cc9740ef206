# TypeScript LangGraph Project

This project demonstrates using LangGraph with OpenRouter API for AI-powered mathematical calculations.

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment variables:**
   - Copy `.env.sample` to `.env`:
     ```bash
     cp .env.sample .env
     ```
   - Edit `.env` and replace the placeholder values with your actual OpenRouter credentials:
     ```
     OPENROUTER_API_KEY=your_actual_api_key_here
     OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
     OPENROUTER_MODEL_ID=mistralai/devstral-small-2505:free
     MODEL_TEMPERATURE=0
     ```

3. **Get your OpenRouter API key:**
   - Visit [OpenRouter](https://openrouter.ai/)
   - Sign up or log in
   - Generate an API key
   - Replace `your_actual_api_key_here` in your `.env` file

## Usage

Open the <PERSON>pyter notebook `notebooks/langgraph.ipynb` and run the cells to see the LangGraph workflow in action.

## Security

- Never commit your `.env` file to version control
- The `.env.sample` file is tracked in git as a template for other developers
- Your actual API keys should only be in your local `.env` file
