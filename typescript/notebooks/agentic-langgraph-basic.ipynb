import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { ChatOpenAI } from "@langchain/openai";
import { END, START } from "@langchain/langgraph";
import { AIMessage, isAIMessage  } from "@langchain/core/messages";

// Tool for addition
const add = tool(
  async ({ a, b }) => (a + b).toString(),
  {
    name: "add",
    description: "Adds two numbers.",
    schema: z.object({ a: z.number(), b: z.number() }),
  }
);

// Tool for subtraction
const subtract = tool(
  async ({ a, b }) => (a - b).toString(),
  {
    name: "subtract",
    description: "Subtracts the second number from the first.",
    schema: z.object({ a: z.number(), b: z.number() }),
  }
);

// Tool for multiplication
const multiply = tool(
  async ({ a, b }) => (a * b).toString(),
  {
    name: "multiply",
    description: "Multiplies two numbers.",
    schema: z.object({ a: z.number(), b: z.number() }),
  }
);

// Tool for division
const divide = tool(
  async ({ a, b }) => (a / b).toString(),
  {
    name: "divide",
    description: "Divides the first number by the second.",
    schema: z.object({ a: z.number(), b: z.number() }),
  }
);

const tools = [add, subtract, multiply, divide];

import { MessagesAnnotation, StateGraph } from "@langchain/langgraph";

const graphState = MessagesAnnotation;


import * as dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

const model = new ChatOpenAI({
  model: process.env.OPENROUTER_MODEL_ID || "mistralai/devstral-small-2505:free",
  temperature: parseFloat(process.env.MODEL_TEMPERATURE || "0"),
  apiKey: process.env.OPENROUTER_API_KEY,
  configuration: {
    baseURL: process.env.OPENROUTER_BASE_URL || "https://openrouter.ai/api/v1",
  },
});

const modelWithTools = model.bindTools(tools);

const agentNode = async (state: typeof MessagesAnnotation.State) => {
    const response = await modelWithTools.invoke(state.messages);
    return { messages: [response] };
};

const toolNode = new ToolNode(tools);

const shouldContinue = (state: typeof MessagesAnnotation.State) => {
  const lastMessage = state.messages[state.messages.length - 1] as AIMessage;
  return lastMessage.tool_calls && lastMessage.tool_calls.length > 0 ? "tools" : END;
};

const workflow = new StateGraph(graphState)
  .addNode("agent", agentNode)
  .addNode("tools", toolNode)
  .addEdge(START, "agent")
  .addConditionalEdges("agent", shouldContinue)
  .addEdge("tools", "agent");

const app = workflow.compile();

// Function to pretty-print the result
const prettyPrintResult = (result) => {
  // Extract the user's query and the AI's final response
  const userMessage = result.messages[0];
  const aiMessage = result.messages[result.messages.length - 1];

  // Extract token usage from the response metadata
  const usage = aiMessage.usage_metadata || aiMessage.response_metadata?.tokenUsage || {};
  const inputTokens = usage.input_tokens || usage.promptTokens || 'N/A';
  const outputTokens = usage.output_tokens || usage.completionTokens || 'N/A';
  const totalTokens = usage.total_tokens || usage.totalTokens || 'N/A';

  // Remove the extra backslashes for cleaner rendering in the console
  const cleanedContent = aiMessage.content.replace(/\\\\/g, '\\');

  console.log("=========================================");
  console.log("          LangGraph Execution          ");
  console.log("=========================================");

  console.log("\n👤 Input Query:");
  console.log(`   ${userMessage.content}`);

  console.log("\n🤖 Assistant's Response:");
  // Indent the cleaned response for readability
  cleanedContent.split('\n').forEach(line => console.log(`   ${line}`));

  console.log("\n-----------------------------------------");
  console.log("📊 Token Usage:");
  console.log(`   - Input Tokens:  ${inputTokens}`);
  console.log(`   - Output Tokens: ${outputTokens}`);
  console.log(`   - Total Tokens:  ${totalTokens}`);
  console.log("=========================================");
};


const result = await app.invoke({
  messages: [
    {
      content: "Provide mathematical expressions in plain text format without LaTeX formatting. Use simple symbols like x, ÷, and = instead of LaTeX commands.",
      role: "system",
    },
    {
      content: "In the following order, What is 100 times 5 plus 9 minus 49 divided by 3?",
      role: "user",
    },
  ],
});

prettyPrintResult(result);