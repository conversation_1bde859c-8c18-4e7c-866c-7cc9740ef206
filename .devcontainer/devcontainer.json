{"name": "JupyterLab with TypeScript, Go, Java, Scala Kernels", "build": {"dockerfile": "Dockerfile"}, "features": {}, "postCreateCommand": "mkdir -p typescript/notebooks go/notebooks java/notebooks scala/notebooks && cd typescript && npm init -y && cd ../go && go mod init jupyter-go-project && cd ../java && echo '<?xml version=\"1.0\"?><project><modelVersion>4.0.0</modelVersion><groupId>jupyter</groupId><artifactId>java-notebooks</artifactId><version>1.0.0</version></project>' > pom.xml && cd ../scala && echo 'scalaVersion := \"3.3.0\"' > build.sbt && cd .. && jupyter kernelspec list", "customizations": {"vscode": {"extensions": ["ms-python.python", "ms-toolsai.jupyter", "ms-vscode.vscode-typescript-next", "golang.go", "redhat.java", "scala-lang.scala"], "settings": {"jupyter.interactiveWindow.textEditor.executeSelection": true, "jupyter.askForKernelRestart": false, "jupyter.kernels.filter": [], "jupyter.kernels.excludePythonEnvironments": false}}}, "forwardPorts": [8888], "portsAttributes": {"8888": {"label": "JupyterLab", "onAutoForward": "openBrowser"}}, "remoteUser": "vscode"}